/**
 * 装饰管理器
 * 整合家具摆放系统的所有功能，提供统一的接口
 */

import { Vec2 } from "cc";
import {
    FurnitureTemplate,
    PlacedFurniture,
    Rotation,
    PlacementResult,
    RoomScoreDetails,
    FurnitureTheme
} from "./DecorationDefine";
import { FurnitureManager } from "./FurnitureManager";
import { RoomGrid } from "./RoomGrid";
import { RoomScoreCalculator } from "./RoomScoreCalculator";
import { RandomizeResult } from "./FurnitureRandomizer";
import { IDataLoader } from "../data_loader";
import { EventTargetMgr } from "../EventTargetMgr";
import { EventTag } from "../EventId";

/**
 * 装饰事件标签
 */
export enum DecorationEventTag {
    FurniturePlaced = "FurniturePlaced",
    FurnitureRemoved = "FurnitureRemoved",
    FurnitureMoved = "FurnitureMoved",
    FurnitureRotated = "FurnitureRotated",
    ScoreChanged = "DecorationScoreChanged"
}

export class DecorationMgr {
    private static _instance: DecorationMgr;

    public static getInstance(): DecorationMgr {
        return this._instance || (this._instance = new DecorationMgr());
    }

    private furnitureManager: FurnitureManager;
    private scoreCalculator: RoomScoreCalculator;
    private currentScore: RoomScoreDetails | null = null;

    private constructor() {
        this.furnitureManager = FurnitureManager.getInstance();
        this.scoreCalculator = new RoomScoreCalculator();
    }

    /**
     * 初始化装饰管理器
     */
    async init() {
        await this.furnitureManager.init();
        this.calculateScore();
    }

    /**
     * 获取所有家具模板
     */
    getAllFurnitureTemplates(): FurnitureTemplate[] {
        return this.furnitureManager.getAllFurnitureTemplates();
    }

    /**
     * 获取家具模板
     */
    getFurnitureTemplate(templateId: number): FurnitureTemplate | null {
        return this.furnitureManager.getFurnitureTemplate(templateId);
    }

    /**
     * 放置家具
     */
    placeFurniture(templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): PlacementResult {
        const result = this.furnitureManager.placeFurniture(templateId, position, rotation);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(DecorationEventTag.FurniturePlaced, templateId, position, rotation);
            EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 移除家具
     */
    removeFurniture(furnitureId: string): boolean {
        const success = this.furnitureManager.removeFurniture(furnitureId);

        if (success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureRemoved, furnitureId);
            EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
        }

        return success;
    }

    /**
     * 移动家具
     */
    moveFurniture(furnitureId: string, newPosition: Vec2): PlacementResult {
        const result = this.furnitureManager.moveFurniture(furnitureId, newPosition);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureMoved, furnitureId, newPosition);
            EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 旋转家具
     */
    rotateFurniture(furnitureId: string, newRotation: Rotation): PlacementResult {
        const result = this.furnitureManager.rotateFurniture(furnitureId, newRotation);

        if (result.success) {
            this.calculateScore();
            EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureRotated, furnitureId, newRotation);
            EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
        }

        return result;
    }

    /**
     * 获取已放置的家具
     */
    getPlacedFurnitures(): PlacedFurniture[] {
        return this.furnitureManager.getPlacedFurnitures();
    }

    /**
     * 获取房间格子
     */
    getRoomGrid(): RoomGrid | null {
        return this.furnitureManager.getRoomGrid();
    }

    /**
     * 计算当前房间评分
     */
    private calculateScore() {
        const placedFurnitures = this.furnitureManager.getPlacedFurnitures();
        const furnitureTemplates = new Map<number, FurnitureTemplate>();

        // 构建模板映射
        this.furnitureManager.getAllFurnitureTemplates().forEach(template => {
            furnitureTemplates.set(template.id, template);
        });

        const roomGrid = this.furnitureManager.getRoomGrid();
        if (roomGrid) {
            const roomSize = roomGrid.getRoomSize();
            this.currentScore = this.scoreCalculator.calculateRoomScore(
                placedFurnitures,
                furnitureTemplates,
                roomSize
            );
        } else {
            this.currentScore = null;
        }
    }

    /**
     * 获取当前房间评分详情
     */
    getRoomScoreDetails(): RoomScoreDetails | null {
        return this.currentScore;
    }

    /**
     * 获取总评分
     */
    getTotalScore(): number {
        return this.currentScore?.totalScore || 0;
    }

    /**
     * 获取主导主题
     */
    getDominantTheme(): FurnitureTheme | null {
        return this.currentScore?.dominantTheme || null;
    }

    /**
     * 获取主题名称
     */
    getDominantThemeName(): string {
        const theme = this.getDominantTheme();
        return theme ? this.scoreCalculator.getThemeName(theme) : "无主题";
    }

    /**
     * 检查家具是否可以放置
     */
    canPlaceFurniture(templateId: number, position: Vec2, rotation: Rotation = Rotation.Deg0): boolean {
        const template = this.furnitureManager.getFurnitureTemplate(templateId);
        if (!template) return false;
        
        const roomGrid = this.furnitureManager.getRoomGrid();
        if (!roomGrid) return false;
        
        // 这里可以添加更复杂的验证逻辑
        return true;
    }

    /**
     * 获取推荐的摆放位置
     */
    getRecommendedPosition(templateId: number, preferredPosition: Vec2): Vec2 | null {
        // TODO: 实现推荐位置算法
        return null;
    }

    /**
     * 随机变化家具位置和角度
     */
    randomizeFurniture(furnitureId: string): RandomizeResult | null {
        const result = this.furnitureManager.randomizeFurniture(furnitureId);

        if (result && result.success) {
            // 应用随机变化结果
            const applied = this.furnitureManager.applyRandomizeResult(result);
            if (applied) {
                this.calculateScore();
                // 发送家具变化事件（根据变化类型发送不同事件）
                if (result.changeType === 'position' || result.changeType === 'both') {
                    EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureMoved, furnitureId, result.newFurniture?.position);
                }
                if (result.changeType === 'rotation' || result.changeType === 'both') {
                    EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureRotated, furnitureId, result.newFurniture?.rotation);
                }
                EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
            }
        }

        return result;
    }

    /**
     * 清空所有家具
     */
    clearAllFurniture(): void {
        const placedFurnitures = this.furnitureManager.getPlacedFurnitures();

        if (placedFurnitures.length === 0) {
            return; // 没有家具需要清空
        }

        const furnitureIds = placedFurnitures.map(f => f.id);

        // 批量移除家具，避免每次都触发事件
        furnitureIds.forEach(id => {
            this.furnitureManager.removeFurniture(id);
        });

        this.calculateScore();

        // 发送批量移除事件
        EventTargetMgr.instance.sendEvent(DecorationEventTag.FurnitureRemoved, furnitureIds);
        EventTargetMgr.instance.sendEvent(DecorationEventTag.ScoreChanged, this.currentScore);
    }

    /**
     * 获取房间使用率
     */
    getRoomUsageRate(): number {
        const roomGrid = this.furnitureManager.getRoomGrid();
        if (!roomGrid) return 0;
        
        const roomSize = roomGrid.getRoomSize();
        const totalGrids = roomSize.x * roomSize.y;
        const occupiedPositions = roomGrid.getOccupiedPositions();
        
        return occupiedPositions.length / totalGrids;
    }

    /**
     * 获取评分等级描述
     */
    getScoreGrade(): string {
        const score = this.getTotalScore();
        if (score >= 90) return "完美";
        if (score >= 80) return "优秀";
        if (score >= 70) return "良好";
        if (score >= 60) return "一般";
        if (score >= 40) return "较差";
        return "糟糕";
    }

    /**
     * 获取评分颜色（用于UI显示）
     */
    getScoreColor(): string {
        const score = this.getTotalScore();
        if (score >= 90) return "#FFD700"; // 金色
        if (score >= 80) return "#FF6B35"; // 橙色
        if (score >= 70) return "#4ECDC4"; // 青色
        if (score >= 60) return "#45B7D1"; // 蓝色
        if (score >= 40) return "#96CEB4"; // 绿色
        return "#FFEAA7"; // 黄色
    }

    /**
     * 导出房间布局（用于分享或保存模板）
     */
    exportRoomLayout(): string {
        const placedFurnitures = this.furnitureManager.getPlacedFurnitures();
        const layoutData = {
            furnitures: placedFurnitures.map(f => ({
                templateId: f.templateId,
                position: { x: f.position.x, y: f.position.y },
                rotation: f.rotation
            })),
            timestamp: Date.now()
        };
        
        return JSON.stringify(layoutData);
    }

    /**
     * 导入房间布局
     */
    importRoomLayout(layoutJson: string): boolean {
        try {
            const layoutData = JSON.parse(layoutJson);
            
            // 清空当前家具
            this.clearAllFurniture();
            
            // 放置新家具
            let successCount = 0;
            layoutData.furnitures.forEach((furnitureData: any) => {
                const result = this.placeFurniture(
                    furnitureData.templateId,
                    new Vec2(furnitureData.position.x, furnitureData.position.y),
                    furnitureData.rotation
                );
                if (result.success) {
                    successCount++;
                }
            });
            
            console.log(`导入房间布局完成，成功放置 ${successCount}/${layoutData.furnitures.length} 个家具`);
            return successCount === layoutData.furnitures.length;
            
        } catch (error) {
            console.error('导入房间布局失败:', error);
            return false;
        }
    }

    /**
     * 数据加载器
     */
    loader: IDataLoader = {
        name: "DecorationMgr",
        total: async () => 1,
        load: async (update) => {
            await this.init();
            update(1);
        },
    };
}
