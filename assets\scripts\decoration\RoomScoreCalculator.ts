/**
 * 房间评分计算器
 * 根据主题匹配度、家具数量价值、布局美观度计算房间评分
 */

import { Vec2 } from "cc";
import { 
    FurnitureTemplate, 
    PlacedFurniture, 
    FurnitureTheme, 
    RoomScoreDetails, 
    ScoreWeights 
} from "./DecorationDefine";

export class RoomScoreCalculator {
    private scoreWeights: ScoreWeights = {
        themeWeight: 0.5,    // 主题权重50%
        valueWeight: 0.3,    // 价值权重30%
        layoutWeight: 0.2    // 布局权重20%
    };

    /**
     * 计算房间总评分
     */
    calculateRoomScore(
        placedFurnitures: PlacedFurniture[], 
        furnitureTemplates: Map<number, FurnitureTemplate>,
        roomSize: Vec2
    ): RoomScoreDetails {
        // 获取家具模板信息
        const furnitureInfos = placedFurnitures.map(furniture => {
            const template = furnitureTemplates.get(furniture.templateId);
            return { furniture, template };
        }).filter((info): info is { furniture: PlacedFurniture; template: FurnitureTemplate } =>
            info.template !== undefined
        );

        // 计算各项得分
        const themeScore = this.calculateThemeScore(furnitureInfos);
        const quantityScore = this.calculateQuantityScore(furnitureInfos.length);
        const valueScore = this.calculateValueScore(furnitureInfos);
        const layoutScore = this.calculateLayoutScore(furnitureInfos, roomSize);

        // 计算总分
        const totalScore = 
            themeScore * this.scoreWeights.themeWeight +
            (quantityScore + valueScore) * this.scoreWeights.valueWeight +
            layoutScore * this.scoreWeights.layoutWeight;

        // 确定主导主题
        const dominantTheme = this.getDominantTheme(furnitureInfos);

        return {
            themeScore,
            quantityScore,
            valueScore,
            layoutScore,
            totalScore: Math.round(totalScore),
            dominantTheme
        };
    }

    /**
     * 计算主题匹配度得分
     */
    private calculateThemeScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        // 统计各主题的家具数量
        const themeCount = new Map<FurnitureTheme, number>();
        furnitureInfos.forEach(info => {
            const theme = info.template.properties.theme;
            themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
        });

        // 找到最多的主题
        let maxCount = 0;
        let dominantTheme: FurnitureTheme | null = null;
        themeCount.forEach((count, theme) => {
            if (count > maxCount) {
                maxCount = count;
                dominantTheme = theme;
            }
        });

        if (!dominantTheme) return 0;

        // 计算主题一致性得分
        const totalFurniture = furnitureInfos.length;
        const themeConsistency = maxCount / totalFurniture;
        
        // 基础分数 + 一致性奖励
        let themeScore = 50 + (themeConsistency * 50);
        
        // 如果有多个主题但搭配合理，给予额外奖励
        if (themeCount.size > 1) {
            const harmoniousBonus = this.calculateHarmoniousBonus(themeCount, dominantTheme);
            themeScore += harmoniousBonus;
        }

        return Math.min(100, themeScore);
    }

    /**
     * 计算和谐搭配奖励
     */
    private calculateHarmoniousBonus(themeCount: Map<FurnitureTheme, number>, dominantTheme: FurnitureTheme): number {
        // 定义主题搭配关系
        const harmoniousThemes = new Map<FurnitureTheme, FurnitureTheme[]>([
            [FurnitureTheme.Modern, [FurnitureTheme.Minimalist, FurnitureTheme.Industrial]],
            [FurnitureTheme.Classic, [FurnitureTheme.Natural]],
            [FurnitureTheme.Natural, [FurnitureTheme.Classic, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Industrial, [FurnitureTheme.Modern, FurnitureTheme.Minimalist]],
            [FurnitureTheme.Minimalist, [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]]
        ]);

        const compatibleThemes = harmoniousThemes.get(dominantTheme) || [];
        let bonus = 0;

        themeCount.forEach((count, theme) => {
            if (theme !== dominantTheme && compatibleThemes.includes(theme)) {
                bonus += count * 2; // 每个和谐搭配的家具+2分
            } else if (theme !== dominantTheme) {
                bonus -= count * 3; // 每个不和谐的家具-3分
            }
        });

        return Math.max(-20, Math.min(20, bonus));
    }

    /**
     * 计算家具数量得分
     */
    private calculateQuantityScore(furnitureCount: number): number {
        // 数量得分曲线：0-5个家具线性增长，5-10个缓慢增长，10个以上不再增长
        if (furnitureCount <= 5) {
            return furnitureCount * 10; // 每个家具10分，最高50分
        } else if (furnitureCount <= 10) {
            return 50 + (furnitureCount - 5) * 5; // 5个以上每个家具5分，最高75分
        } else {
            return 75; // 10个以上不再增长
        }
    }

    /**
     * 计算家具价值得分
     */
    private calculateValueScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        // 计算总价值和平均等级
        let totalValue = 0;
        let totalLevel = 0;

        furnitureInfos.forEach(info => {
            totalValue += info.template.properties.value;
            totalLevel += info.template.properties.level;
        });

        const averageValue = totalValue / furnitureInfos.length;
        const averageLevel = totalLevel / furnitureInfos.length;

        // 价值得分：基于平均价值和等级
        const valueScore = Math.min(50, averageValue * 2); // 价值得分最高50分
        const levelScore = Math.min(25, averageLevel * 5); // 等级得分最高25分

        return valueScore + levelScore;
    }

    /**
     * 计算布局美观度得分
     */
    private calculateLayoutScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        if (furnitureInfos.length === 0) return 0;

        let layoutScore = 50; // 基础分数

        // 1. 计算分布均匀度
        const distributionScore = this.calculateDistributionScore(furnitureInfos, roomSize);
        layoutScore += distributionScore;

        // 2. 计算对称性
        const symmetryScore = this.calculateSymmetryScore(furnitureInfos, roomSize);
        layoutScore += symmetryScore;

        // 3. 计算空间利用率
        const utilizationScore = this.calculateUtilizationScore(furnitureInfos, roomSize);
        layoutScore += utilizationScore;

        // 4. 计算美观度加成
        const beautyScore = this.calculateBeautyScore(furnitureInfos);
        layoutScore += beautyScore;

        // 5. 计算挂饰类家具奖励
        const wallDecorationScore = this.calculateWallDecorationScore(furnitureInfos);
        layoutScore += wallDecorationScore;

        return Math.max(0, Math.min(100, layoutScore));
    }

    /**
     * 计算分布均匀度得分
     */
    private calculateDistributionScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        if (furnitureInfos.length <= 1) return 0;

        // 计算家具重心
        const centerX = furnitureInfos.reduce((sum, info) => sum + info.furniture.position.x, 0) / furnitureInfos.length;
        const centerY = furnitureInfos.reduce((sum, info) => sum + info.furniture.position.y, 0) / furnitureInfos.length;

        // 计算与房间中心的偏差
        const roomCenterX = roomSize.x / 2;
        const roomCenterY = roomSize.y / 2;
        const deviation = Math.sqrt(Math.pow(centerX - roomCenterX, 2) + Math.pow(centerY - roomCenterY, 2));
        const maxDeviation = Math.sqrt(Math.pow(roomSize.x / 2, 2) + Math.pow(roomSize.y / 2, 2));

        // 分布得分：偏差越小得分越高
        return Math.max(-10, 10 - (deviation / maxDeviation) * 20);
    }

    /**
     * 计算对称性得分
     */
    private calculateSymmetryScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        // 简化的对称性计算：检查左右对称
        const centerX = roomSize.x / 2;
        let symmetryScore = 0;

        furnitureInfos.forEach(info => {
            const mirrorX = centerX * 2 - info.furniture.position.x;
            const hasMirror = furnitureInfos.some(other => 
                Math.abs(other.furniture.position.x - mirrorX) <= 1 &&
                Math.abs(other.furniture.position.y - info.furniture.position.y) <= 1
            );
            
            if (hasMirror) {
                symmetryScore += 2;
            }
        });

        return Math.min(10, symmetryScore);
    }

    /**
     * 计算空间利用率得分
     */
    private calculateUtilizationScore(
        furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>, 
        roomSize: Vec2
    ): number {
        const totalRoomArea = roomSize.x * roomSize.y;
        const occupiedArea = furnitureInfos.reduce((sum, info) => 
            sum + info.furniture.currentSize.x * info.furniture.currentSize.y, 0);
        
        const utilization = occupiedArea / totalRoomArea;
        
        // 最佳利用率在20%-60%之间
        if (utilization >= 0.2 && utilization <= 0.6) {
            return 10;
        } else if (utilization < 0.2) {
            return utilization * 50; // 利用率过低扣分
        } else {
            return Math.max(-10, 10 - (utilization - 0.6) * 25); // 利用率过高扣分
        }
    }

    /**
     * 计算美观度得分
     */
    private calculateBeautyScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        if (furnitureInfos.length === 0) return 0;

        const totalBeauty = furnitureInfos.reduce((sum, info) => sum + info.template.properties.beauty, 0);
        const averageBeauty = totalBeauty / furnitureInfos.length;

        return Math.min(15, averageBeauty * 3);
    }

    /**
     * 计算挂饰类家具得分
     */
    private calculateWallDecorationScore(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): number {
        const wallDecorations = furnitureInfos.filter(info =>
            info.template.properties.isWallDecoration
        );

        if (wallDecorations.length === 0) return 0;

        // 每个挂饰类家具+8分
        return Math.min(20, wallDecorations.length * 8);
    }

    /**
     * 获取主导主题
     */
    private getDominantTheme(furnitureInfos: Array<{furniture: PlacedFurniture, template: FurnitureTemplate}>): FurnitureTheme | null {
        if (furnitureInfos.length === 0) return null;

        const themeCount = new Map<FurnitureTheme, number>();
        furnitureInfos.forEach(info => {
            const theme = info.template.properties.theme;
            themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
        });

        let maxCount = 0;
        let dominantTheme: FurnitureTheme | null = null;
        themeCount.forEach((count, theme) => {
            if (count > maxCount) {
                maxCount = count;
                dominantTheme = theme;
            }
        });

        return dominantTheme;
    }

    /**
     * 获取主题名称
     */
    getThemeName(theme: FurnitureTheme): string {
        switch (theme) {
            case FurnitureTheme.Modern:
                return "现代风格";
            case FurnitureTheme.Classic:
                return "古典风格";
            case FurnitureTheme.Natural:
                return "自然风格";
            case FurnitureTheme.Industrial:
                return "工业风格";
            case FurnitureTheme.Minimalist:
                return "简约风格";
            default:
                return "未知风格";
        }
    }

    /**
     * 设置评分权重
     */
    setScoreWeights(weights: ScoreWeights) {
        this.scoreWeights = { ...weights };
    }

    /**
     * 获取当前评分权重
     */
    getScoreWeights(): ScoreWeights {
        return { ...this.scoreWeights };
    }
}
