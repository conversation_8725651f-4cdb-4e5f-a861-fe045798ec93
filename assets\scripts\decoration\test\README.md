# 装饰系统测试文件夹

本文件夹包含装饰系统的各种测试脚本，用于验证系统功能的正确性。

## 测试文件说明

### AdjacentBonusTest.ts
**相邻加成功能测试脚本**

测试内容：
- 基础相邻检测机制
- 主题匹配相邻加成
- 特殊组合奖励（桌椅组合、挂饰装点等）
- 复杂布局综合测试
- 分散vs集中摆放对比

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `AdjacentBonusTest` 组件
3. 运行场景，查看控制台输出

### MultiRoomTest.ts
**双房间系统测试脚本**

测试内容：
- 默认房间模板验证
- 多房间管理器功能测试
- 装饰管理器集成测试
- 房间配置和家具摆放

使用方法：
1. 在Cocos Creator中创建空节点
2. 添加 `MultiRoomTest` 组件
3. 运行场景，查看控制台输出

## 测试数据

### 模拟家具模板
测试脚本包含以下模拟家具：

**AdjacentBonusTest.ts:**
- 现代椅子 (1×1, 现代风格, Small)
- 古典床 (2×1, 古典风格, Medium)
- 工业桌子 (2×2, 工业风格, Large)
- 现代沙发 (2×1, 现代风格, Medium)
- 古典壁画 (1×1, 古典风格, WallDecoration)

**MultiRoomTest.ts:**
- 测试椅子 (1×1, 现代风格)
- 测试桌子 (2×1, 现代风格)
- 测试沙发 (2×2, 古典风格)
- 测试壁画 (1×1, 古典风格, 挂饰类)

## 预期测试结果

### 相邻加成测试
```
=== 相邻加成功能测试开始 ===
--- 测试基础相邻检测 ---
两个相邻椅子的评分:
- 主题得分: 75
- 相邻得分: 7
- 总分: 45

两个不相邻椅子的评分:
- 主题得分: 75
- 相邻得分: 0
- 总分: 42

相邻加成差异: +7分
```

### 双房间系统测试
```
=== 双房间系统测试开始 ===
--- 测试默认房间模板 ---
获取到 4 个房间模板
房间A: 房间A, 尺寸: 10×10
房间A布局验证: true
房间A可用空间: 52
房间A邻墙格子: 28

--- 测试多房间管理器 ---
多房间管理器初始化完成
房间数量: 2, 房间ID: [room_a, room_b]
在房间A放置家具1: 成功
在房间B放置家具2: 成功
当前评分: 65 (主题:45, 数量:20, 价值:15, 布局:55)
```

## 注意事项

1. **依赖关系**: 测试脚本依赖于装饰系统的核心类，确保相关文件已正确导入
2. **异步操作**: MultiRoomTest 包含异步操作，需要等待初始化完成
3. **控制台输出**: 所有测试结果都输出到控制台，运行时请打开开发者工具查看
4. **错误处理**: 测试脚本包含错误捕获，如果测试失败会显示详细错误信息

## 扩展测试

如需添加新的测试用例，可以：

1. **扩展现有测试**: 在现有测试方法中添加新的测试场景
2. **创建新测试文件**: 按照现有模式创建新的测试脚本
3. **添加性能测试**: 测试大量家具摆放时的性能表现
4. **添加边界测试**: 测试极端情况下的系统行为

## 测试最佳实践

1. **独立性**: 每个测试方法应该独立运行，不依赖其他测试的结果
2. **清理**: 测试完成后应该清理创建的数据，避免影响后续测试
3. **断言**: 添加明确的断言来验证测试结果
4. **文档**: 为新增的测试用例添加清晰的注释和说明
