<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间装饰系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
            color: white;
        }

        .main-content {
            display: flex;
            gap: 20px;
        }

        .room-container {
            flex: 2;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .rooms-display {
            display: flex;
            gap: 30px;
            justify-content: center;
            align-items: flex-start;
            margin: 20px 0;
        }

        .room-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .room-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px 16px;
            border-radius: 6px;
            color: white;
        }

        .room-a-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .room-b-title {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        .room-grid {
            display: grid;
            gap: 1px;
            background: #ddd;
            border: 2px solid #333;
            margin: 20px 0;
            padding: 5px;
        }

        .room-a {
            grid-template-columns: repeat(10, 30px);
            grid-template-rows: repeat(10, 30px);
        }

        .room-b {
            grid-template-columns: repeat(6, 30px);
            grid-template-rows: repeat(6, 30px);
        }

        .room-tabs {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }

        .tab-btn {
            padding: 10px 20px;
            border: 2px solid #4ecdc4;
            background: white;
            color: #4ecdc4;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
        }

        .tab-btn.active {
            background: #4ecdc4;
            color: white;
        }

        .tab-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .grid-cell {
            width: 30px;
            height: 30px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .cell-empty { background: #fff; }
        .cell-wall { background: #666; color: white; }
        .cell-obstacle { background: #333; color: white; }
        .cell-occupied { background: #ff6b6b; color: white; }

        .furniture-panel {
            flex: 1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .furniture-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }

        .furniture-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .furniture-item.selected {
            border-color: #4ecdc4;
            background: #e8f8f5;
        }

        .furniture-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            margin-right: 10px;
        }

        .theme-modern { background: linear-gradient(45deg, #667eea, #764ba2); color: white; }
        .theme-classic { background: linear-gradient(45deg, #8B4513, #D2691E); color: white; }
        .theme-natural { background: linear-gradient(45deg, #228B22, #32CD32); color: white; }
        .theme-industrial { background: linear-gradient(45deg, #696969, #A9A9A9); color: white; }
        .theme-minimalist { background: linear-gradient(45deg, #F5F5F5, #DCDCDC); color: #333; }

        .furniture-info {
            flex: 1;
        }

        .furniture-name {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .furniture-props {
            font-size: 12px;
            color: #666;
        }

        .score-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border: 2px solid #4ecdc4;
        }

        .score-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .total-score {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
            text-align: center;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(45deg, #ffeaa7, #fab1a0);
            border-radius: 10px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
        }

        .btn-primary { background: #4ecdc4; color: white; }
        .btn-secondary { background: #95a5a6; color: white; }
        .btn-danger { background: #e74c3c; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .legend {
            display: flex;
            gap: 15px;
            margin: 10px 0;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 房间装饰系统测试</h1>
            <p>基于当前项目的房间摆放逻辑和评分系统</p>
        </div>

        <div class="main-content">
            <div class="room-container">
                <h3>双房间布局系统</h3>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color cell-empty"></div>
                        <span>空地(0)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color cell-wall"></div>
                        <span>邻墙(1)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color cell-obstacle"></div>
                        <span>障碍(2)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color cell-occupied"></div>
                        <span>已占用</span>
                    </div>
                </div>

                <div class="rooms-display">
                    <div class="room-section">
                        <div class="room-title room-a-title">房间A (10×10)</div>
                        <div id="roomA" class="room-grid room-a"></div>
                        <div style="margin-top: 10px; font-size: 12px; color: #666;">
                            家具数量: <span id="roomACount">0</span>
                        </div>
                    </div>

                    <div class="room-section">
                        <div class="room-title room-b-title">房间B (6×6)</div>
                        <div id="roomB" class="room-grid room-b"></div>
                        <div style="margin-top: 10px; font-size: 12px; color: #666;">
                            家具数量: <span id="roomBCount">0</span>
                        </div>
                    </div>
                </div>

                <div class="controls">
                    <button class="btn btn-primary" onclick="placeFurniture()">放置家具</button>
                    <button class="btn btn-secondary" onclick="rotateFurniture()">旋转 (90°)</button>
                    <button class="btn btn-danger" onclick="removeFurniture()">移除家具</button>
                    <button class="btn btn-secondary" onclick="clearRoom('A')">清空房间A</button>
                    <button class="btn btn-secondary" onclick="clearRoom('B')">清空房间B</button>
                    <button class="btn btn-secondary" onclick="clearAllRooms()">清空所有房间</button>
                    <button class="btn btn-primary" onclick="randomPlace()">随机摆放</button>
                </div>

                <div id="status" class="status status-info">
                    点击选择家具，然后点击任意房间格子进行摆放
                </div>
            </div>

            <div class="furniture-panel">
                <h3>家具列表</h3>
                <div id="furnitureList"></div>
                
                <div class="score-panel">
                    <h3>评分详情</h3>
                    <div id="scoreDetails"></div>
                    <div id="totalScore" class="total-score">总分: 0</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 枚举定义
        const FurnitureType = {
            Small: 1,
            Medium: 2,
            Large: 3,
            WallDecoration: 4
        };

        const FurnitureTheme = {
            Modern: 1,
            Classic: 2,
            Natural: 3,
            Industrial: 4,
            Minimalist: 5
        };

        const GridState = {
            Empty: 0,
            WallAdjacent: 1,
            Obstacle: 2,
            Occupied: 3
        };

        const Rotation = {
            Deg0: 0,
            Deg90: 90,
            Deg180: 180,
            Deg270: 270
        };

        // 全局变量
        var rooms = {
            A: {
                name: "房间A",
                size: { x: 10, y: 10 },
                grid: [],
                layout: [
                    [2,2,2,1,1,1,1,2,2,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,1,0,0,0,0,0,0,1,2],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [1,0,0,0,0,0,0,0,0,1],
                    [2,1,0,0,0,0,0,0,1,2],
                    [2,2,1,0,0,0,0,1,2,2],
                    [2,2,2,1,1,1,1,2,2,2]
                ]
            },
            B: {
                name: "房间B",
                size: { x: 6, y: 6 },
                grid: [],
                layout: [
                    [2,2,1,1,2,2],
                    [2,1,0,0,1,2],
                    [1,0,0,0,0,1],
                    [1,0,0,0,0,1],
                    [2,1,0,0,1,2],
                    [2,2,1,1,2,2]
                ]
            }
        };
        var placedFurnitures = [];
        var furnitureTemplates = new Map();
        var selectedFurniture = null;
        var selectedPosition = null;

        // 家具模板数据
        var furnitureData = [
            {
                id: 1,
                name: "现代椅子",
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                icon: "C",
                description: "简洁现代的椅子，舒适实用",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 8,
                    beauty: 6
                }
            },
            {
                id: 2,
                name: "古典床",
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                icon: "B",
                description: "优雅的古典风格床铺",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 15,
                    beauty: 8
                }
            },
            {
                id: 3,
                name: "工业桌子",
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                icon: "T",
                description: "粗犷的工业风格桌子",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 12,
                    beauty: 5
                }
            },
            {
                id: 4,
                name: "现代沙发",
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                icon: "S",
                description: "舒适的现代风格沙发",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 3,
                    value: 20,
                    beauty: 10
                }
            },
            {
                id: 5,
                name: "自然书架",
                type: FurnitureType.Medium,
                baseSize: { x: 1, y: 2 },
                icon: "R",
                description: "天然材质的书架",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 2,
                    value: 18,
                    beauty: 7
                }
            },
            {
                id: 6,
                name: "古典壁画",
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                icon: "P",
                description: "典雅的古典壁画",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 25,
                    beauty: 15,
                    isWallDecoration: true
                }
            },
            {
                id: 7,
                name: "现代照片墙",
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                icon: "F",
                description: "现代风格的照片墙",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 2,
                    value: 12,
                    beauty: 12,
                    isWallDecoration: true
                }
            },
            {
                id: 8,
                name: "简约台灯",
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                icon: "L",
                description: "简约风格的台灯，线条简洁",
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 1,
                    value: 6,
                    beauty: 7
                }
            }
        ];

        // 初始化
        function init() {
            initFurnitureTemplates();
            initRoomGrids();
            renderRooms();
            renderFurnitureList();
            updateScore();
            updateRoomCounts();
        }

        function initFurnitureTemplates() {
            furnitureData.forEach(data => {
                furnitureTemplates.set(data.id, data);
            });
        }

        function initRoomGrids() {
            // 初始化房间A和房间B的网格
            Object.keys(rooms).forEach(roomId => {
                var room = rooms[roomId];
                room.grid = [];
                for (var y = 0; y < room.size.y; y++) {
                    var row = [];
                    for (var x = 0; x < room.size.x; x++) {
                        row.push(room.layout[y][x]);
                    }
                    room.grid.push(row);
                }
            });
        }

        function renderRooms() {
            renderRoom('A');
            renderRoom('B');
        }

        function renderRoom(roomId) {
            var room = rooms[roomId];
            var gridElement = document.getElementById('room' + roomId);
            gridElement.innerHTML = '';

            for (var y = 0; y < room.size.y; y++) {
                for (var x = 0; x < room.size.x; x++) {
                    var cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.dataset.room = roomId;

                    var state = room.grid[y][x];
                    var furniture = getFurnitureAt(x, y, roomId);

                    if (furniture) {
                        // 家具占用
                        cell.className += ' cell-occupied';
                        var template = furnitureTemplates.get(furniture.templateId);
                        cell.textContent = template ? template.icon : '?';
                    } else {
                        // 根据原始状态设置样式
                        switch (state) {
                            case 0: // 空地
                                cell.className += ' cell-empty';
                                cell.textContent = '·';
                                break;
                            case 1: // 邻墙
                                cell.className += ' cell-wall';
                                cell.textContent = '·';
                                break;
                            case 2: // 障碍
                                cell.className += ' cell-obstacle';
                                cell.textContent = '█';
                                break;
                        }
                    }

                    cell.onclick = function() {
                        handleCellClick(parseInt(this.dataset.x), parseInt(this.dataset.y), this.dataset.room);
                    };

                    gridElement.appendChild(cell);
                }
            }
        }

        function renderFurnitureList() {
            var listElement = document.getElementById('furnitureList');
            listElement.innerHTML = '';

            furnitureData.forEach(function(furniture) {
                var item = document.createElement('div');
                item.className = 'furniture-item';
                if (selectedFurniture && selectedFurniture.id === furniture.id) {
                    item.className += ' selected';
                }

                var icon = document.createElement('div');
                icon.className = 'furniture-icon ' + getThemeClass(furniture.properties.theme);
                icon.textContent = furniture.icon;

                var info = document.createElement('div');
                info.className = 'furniture-info';

                var name = document.createElement('div');
                name.className = 'furniture-name';
                name.textContent = furniture.name;

                var props = document.createElement('div');
                props.className = 'furniture-props';
                props.textContent = `${furniture.baseSize.x}×${furniture.baseSize.y} | 价值:${furniture.properties.value} | 美观:${furniture.properties.beauty}`;

                info.appendChild(name);
                info.appendChild(props);
                item.appendChild(icon);
                item.appendChild(info);

                item.onclick = function() {
                    selectFurniture(furniture);
                };

                listElement.appendChild(item);
            });
        }

        function getThemeClass(theme) {
            switch (theme) {
                case FurnitureTheme.Modern: return 'theme-modern';
                case FurnitureTheme.Classic: return 'theme-classic';
                case FurnitureTheme.Natural: return 'theme-natural';
                case FurnitureTheme.Industrial: return 'theme-industrial';
                case FurnitureTheme.Minimalist: return 'theme-minimalist';
                default: return 'theme-modern';
            }
        }

        function selectFurniture(furniture) {
            selectedFurniture = furniture;
            renderFurnitureList();
            showStatus(`已选择: ${furniture.name}`, 'info');
        }

        function handleCellClick(x, y, roomId) {
            selectedPosition = { x: x, y: y, room: roomId };

            if (selectedFurniture) {
                placeFurnitureAt(x, y, roomId);
            } else {
                var furniture = getFurnitureAt(x, y, roomId);
                if (furniture) {
                    removeFurnitureById(furniture.id);
                }
            }
        }

        function getFurnitureAt(x, y, roomId) {
            return placedFurnitures.find(function(furniture) {
                return furniture.room === roomId &&
                       furniture.position.x <= x && x < furniture.position.x + furniture.currentSize.x &&
                       furniture.position.y <= y && y < furniture.position.y + furniture.currentSize.y;
            });
        }

        function placeFurnitureAt(x, y, roomId) {
            if (!selectedFurniture) {
                showStatus('请先选择家具', 'error');
                return;
            }

            var size = getRotatedSize(selectedFurniture.baseSize, Rotation.Deg0);

            // 检查是否可以放置
            if (!canPlaceFurniture(x, y, size, selectedFurniture.properties.isWallDecoration, roomId)) {
                showStatus('无法在此位置放置家具', 'error');
                return;
            }

            // 创建家具实例
            var furniture = {
                id: 'furniture_' + Date.now(),
                templateId: selectedFurniture.id,
                position: { x: x, y: y },
                rotation: Rotation.Deg0,
                currentSize: size,
                placedTime: Date.now(),
                room: roomId
            };

            placedFurnitures.push(furniture);
            renderRoom(roomId);
            updateScore();
            updateRoomCounts();
            showStatus(`成功在${rooms[roomId].name}放置: ${selectedFurniture.name}`, 'success');
        }

        function canPlaceFurniture(x, y, size, isWallDecoration, roomId) {
            var room = rooms[roomId];

            // 检查边界
            if (x < 0 || y < 0 || x + size.x > room.size.x || y + size.y > room.size.y) {
                return false;
            }

            // 检查挂饰类家具是否贴墙
            if (isWallDecoration && !isAdjacentToWall(x, y, roomId)) {
                return false;
            }

            // 检查占用情况
            for (var dy = 0; dy < size.y; dy++) {
                for (var dx = 0; dx < size.x; dx++) {
                    var state = room.grid[y + dy][x + dx];
                    if (state === 2) { // 障碍物
                        return false;
                    }

                    // 检查是否与其他家具冲突
                    var existingFurniture = getFurnitureAt(x + dx, y + dy, roomId);
                    if (existingFurniture) {
                        return false;
                    }
                }
            }

            return true;
        }

        function isAdjacentToWall(x, y, roomId) {
            var room = rooms[roomId];
            return room.grid[y][x] === 1; // 邻墙格子
        }

        function getRotatedSize(baseSize, rotation) {
            if (rotation === Rotation.Deg90 || rotation === Rotation.Deg270) {
                return { x: baseSize.y, y: baseSize.x };
            }
            return { x: baseSize.x, y: baseSize.y };
        }





        // 评分系统
        function calculateRoomScore() {
            if (placedFurnitures.length === 0) {
                return {
                    themeScore: 0,
                    quantityScore: 0,
                    valueScore: 0,
                    layoutScore: 0,
                    totalScore: 0,
                    dominantTheme: null
                };
            }

            var furnitureInfos = placedFurnitures.map(function(furniture) {
                return {
                    furniture: furniture,
                    template: furnitureTemplates.get(furniture.templateId)
                };
            });

            var themeScore = calculateThemeScore(furnitureInfos);
            var quantityScore = calculateQuantityScore(placedFurnitures.length);
            var valueScore = calculateValueScore(furnitureInfos);
            var layoutScore = calculateLayoutScore(furnitureInfos);

            var scoreWeights = {
                themeWeight: 0.5,
                valueWeight: 0.3,
                layoutWeight: 0.2
            };

            var totalScore =
                themeScore * scoreWeights.themeWeight +
                (quantityScore + valueScore) * scoreWeights.valueWeight +
                layoutScore * scoreWeights.layoutWeight;

            var dominantTheme = getDominantTheme(furnitureInfos);

            return {
                themeScore: Math.round(themeScore),
                quantityScore: Math.round(quantityScore),
                valueScore: Math.round(valueScore),
                layoutScore: Math.round(layoutScore),
                totalScore: Math.round(totalScore),
                dominantTheme: dominantTheme
            };
        }

        function calculateThemeScore(furnitureInfos) {
            if (furnitureInfos.length === 0) return 0;

            var themeCount = new Map();
            furnitureInfos.forEach(function(info) {
                var theme = info.template.properties.theme;
                themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
            });

            var maxCount = 0;
            var dominantTheme = null;
            themeCount.forEach(function(count, theme) {
                if (count > maxCount) {
                    maxCount = count;
                    dominantTheme = theme;
                }
            });

            if (!dominantTheme) return 0;

            var totalFurniture = furnitureInfos.length;
            var themeConsistency = maxCount / totalFurniture;

            var themeScore = 50 + (themeConsistency * 50);

            if (themeCount.size > 1) {
                var harmoniousBonus = calculateHarmoniousBonus(themeCount, dominantTheme);
                themeScore += harmoniousBonus;
            }

            return Math.min(100, themeScore);
        }

        function calculateHarmoniousBonus(themeCount, dominantTheme) {
            var harmoniousThemes = new Map([
                [FurnitureTheme.Modern, [FurnitureTheme.Minimalist, FurnitureTheme.Industrial]],
                [FurnitureTheme.Classic, [FurnitureTheme.Natural]],
                [FurnitureTheme.Natural, [FurnitureTheme.Classic, FurnitureTheme.Minimalist]],
                [FurnitureTheme.Industrial, [FurnitureTheme.Modern, FurnitureTheme.Minimalist]],
                [FurnitureTheme.Minimalist, [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]]
            ]);

            var compatibleThemes = harmoniousThemes.get(dominantTheme) || [];
            var bonus = 0;

            themeCount.forEach(function(count, theme) {
                if (theme !== dominantTheme && compatibleThemes.includes(theme)) {
                    bonus += count * 2;
                } else if (theme !== dominantTheme) {
                    bonus -= count * 3;
                }
            });

            return Math.max(-20, Math.min(20, bonus));
        }

        function calculateQuantityScore(furnitureCount) {
            if (furnitureCount <= 5) {
                return furnitureCount * 10;
            } else if (furnitureCount <= 10) {
                return 50 + (furnitureCount - 5) * 5;
            } else {
                return 75;
            }
        }

        function calculateValueScore(furnitureInfos) {
            if (furnitureInfos.length === 0) return 0;

            var totalValue = 0;
            var totalLevel = 0;

            furnitureInfos.forEach(function(info) {
                totalValue += info.template.properties.value;
                totalLevel += info.template.properties.level;
            });

            var averageValue = totalValue / furnitureInfos.length;
            var averageLevel = totalLevel / furnitureInfos.length;

            var valueScore = Math.min(50, averageValue * 2);
            var levelScore = Math.min(25, averageLevel * 5);

            return valueScore + levelScore;
        }

        function calculateLayoutScore(furnitureInfos) {
            if (furnitureInfos.length === 0) return 0;

            var layoutScore = 50;

            // 计算美观度加成
            var totalBeauty = furnitureInfos.reduce(function(sum, info) {
                return sum + info.template.properties.beauty;
            }, 0);
            var averageBeauty = totalBeauty / furnitureInfos.length;
            layoutScore += Math.min(15, averageBeauty * 3);

            // 计算挂饰类家具奖励
            var wallDecorations = furnitureInfos.filter(function(info) {
                return info.template.properties.isWallDecoration;
            });
            layoutScore += Math.min(20, wallDecorations.length * 8);

            // 计算空间利用率
            var totalRoomArea = roomSize.x * roomSize.y;
            var occupiedArea = furnitureInfos.reduce(function(sum, info) {
                return sum + info.furniture.currentSize.x * info.furniture.currentSize.y;
            }, 0);

            var utilization = occupiedArea / totalRoomArea;

            if (utilization >= 0.2 && utilization <= 0.6) {
                layoutScore += 10;
            } else if (utilization < 0.2) {
                layoutScore += utilization * 50;
            } else {
                layoutScore += Math.max(-10, 10 - (utilization - 0.6) * 25);
            }

            return Math.max(0, Math.min(100, layoutScore));
        }

        function getDominantTheme(furnitureInfos) {
            if (furnitureInfos.length === 0) return null;

            var themeCount = new Map();
            furnitureInfos.forEach(function(info) {
                var theme = info.template.properties.theme;
                themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
            });

            var maxCount = 0;
            var dominantTheme = null;
            themeCount.forEach(function(count, theme) {
                if (count > maxCount) {
                    maxCount = count;
                    dominantTheme = theme;
                }
            });

            return dominantTheme;
        }

        function getThemeName(theme) {
            switch (theme) {
                case FurnitureTheme.Modern: return "现代风格";
                case FurnitureTheme.Classic: return "古典风格";
                case FurnitureTheme.Natural: return "自然风格";
                case FurnitureTheme.Industrial: return "工业风格";
                case FurnitureTheme.Minimalist: return "简约风格";
                default: return "未知风格";
            }
        }

        function updateScore() {
            var score = calculateRoomScore();
            var detailsElement = document.getElementById('scoreDetails');
            var totalElement = document.getElementById('totalScore');

            detailsElement.innerHTML = `
                <div class="score-item">
                    <span>主题匹配度:</span>
                    <span>${score.themeScore}/100</span>
                </div>
                <div class="score-item">
                    <span>家具数量:</span>
                    <span>${score.quantityScore}/75</span>
                </div>
                <div class="score-item">
                    <span>家具价值:</span>
                    <span>${score.valueScore}/75</span>
                </div>
                <div class="score-item">
                    <span>布局美观:</span>
                    <span>${score.layoutScore}/100</span>
                </div>
                <div class="score-item">
                    <span>主导主题:</span>
                    <span>${score.dominantTheme ? getThemeName(score.dominantTheme) : '无'}</span>
                </div>
            `;

            totalElement.textContent = `总分: ${score.totalScore}`;

            // 根据分数设置颜色
            if (score.totalScore >= 80) {
                totalElement.style.background = 'linear-gradient(45deg, #00b894, #00cec9)';
            } else if (score.totalScore >= 60) {
                totalElement.style.background = 'linear-gradient(45deg, #fdcb6e, #e17055)';
            } else {
                totalElement.style.background = 'linear-gradient(45deg, #ffeaa7, #fab1a0)';
            }
        }

        function showStatus(message, type) {
            var statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = 'status status-' + type;
        }

        // 控制函数
        function placeFurniture() {
            if (!selectedFurniture) {
                showStatus('请先选择家具', 'error');
                return;
            }
            showStatus('点击房间格子放置家具', 'info');
        }

        function rotateFurniture() {
            if (!selectedPosition) {
                showStatus('请先点击一个家具', 'error');
                return;
            }

            var furniture = getFurnitureAt(selectedPosition.x, selectedPosition.y, selectedPosition.room);
            if (!furniture) {
                showStatus('该位置没有家具', 'error');
                return;
            }

            // 旋转家具
            var newRotation = (furniture.rotation + 90) % 360;
            var template = furnitureTemplates.get(furniture.templateId);
            var newSize = getRotatedSize(template.baseSize, newRotation);

            // 检查旋转后是否还能放置
            if (canPlaceFurnitureRotation(furniture.position.x, furniture.position.y, newSize, furniture.id, template.properties.isWallDecoration, furniture.room)) {
                furniture.rotation = newRotation;
                furniture.currentSize = newSize;
                renderRoom(furniture.room);
                updateScore();
                updateRoomCounts();
                showStatus('家具已旋转', 'success');
            } else {
                showStatus('无法旋转，空间不足', 'error');
            }
        }

        function canPlaceFurnitureRotation(x, y, size, excludeId, isWallDecoration, roomId) {
            var room = rooms[roomId];

            if (x < 0 || y < 0 || x + size.x > room.size.x || y + size.y > room.size.y) {
                return false;
            }

            if (isWallDecoration && !isAdjacentToWall(x, y, roomId)) {
                return false;
            }

            // 临时移除当前家具
            var tempFurnitures = placedFurnitures.filter(function(f) { return f.id !== excludeId; });

            // 检查与其他家具的冲突
            for (var dy = 0; dy < size.y; dy++) {
                for (var dx = 0; dx < size.x; dx++) {
                    var checkX = x + dx;
                    var checkY = y + dy;

                    // 检查是否是障碍物
                    if (room.grid[checkY] && room.grid[checkY][checkX] === 2) {
                        return false;
                    }

                    // 检查是否与其他家具冲突
                    var conflict = tempFurnitures.some(function(furniture) {
                        return furniture.room === roomId &&
                               furniture.position.x <= checkX && checkX < furniture.position.x + furniture.currentSize.x &&
                               furniture.position.y <= checkY && checkY < furniture.position.y + furniture.currentSize.y;
                    });

                    if (conflict) {
                        return false;
                    }
                }
            }

            return true;
        }

        function removeFurniture() {
            if (!selectedPosition) {
                showStatus('请先点击一个家具', 'error');
                return;
            }

            var furniture = getFurnitureAt(selectedPosition.x, selectedPosition.y, selectedPosition.room);
            if (furniture) {
                removeFurnitureById(furniture.id);
            } else {
                showStatus('该位置没有家具', 'error');
            }
        }

        function removeFurnitureById(furnitureId) {
            var index = placedFurnitures.findIndex(function(f) { return f.id === furnitureId; });
            if (index !== -1) {
                var furniture = placedFurnitures[index];
                var template = furnitureTemplates.get(furniture.templateId);
                var roomId = furniture.room;
                placedFurnitures.splice(index, 1);
                renderRoom(roomId);
                updateScore();
                updateRoomCounts();
                showStatus(`已从${rooms[roomId].name}移除: ${template.name}`, 'success');
            }
        }

        function clearRoom(roomId) {
            var roomFurnitures = placedFurnitures.filter(function(f) { return f.room === roomId; });
            placedFurnitures = placedFurnitures.filter(function(f) { return f.room !== roomId; });
            renderRoom(roomId);
            updateScore();
            updateRoomCounts();
            showStatus(`${rooms[roomId].name}已清空 (移除了${roomFurnitures.length}个家具)`, 'success');
        }

        function clearAllRooms() {
            var totalCount = placedFurnitures.length;
            placedFurnitures = [];
            renderRooms();
            updateScore();
            updateRoomCounts();
            showStatus(`所有房间已清空 (移除了${totalCount}个家具)`, 'success');
        }

        function updateRoomCounts() {
            var roomACounts = placedFurnitures.filter(function(f) { return f.room === 'A'; }).length;
            var roomBCounts = placedFurnitures.filter(function(f) { return f.room === 'B'; }).length;

            document.getElementById('roomACount').textContent = roomACounts;
            document.getElementById('roomBCount').textContent = roomBCounts;
        }

        function randomPlace() {
            clearAllRooms();

            var totalPlaced = 0;
            var roomIds = ['A', 'B'];

            // 在每个房间随机摆放家具
            roomIds.forEach(function(roomId) {
                var room = rooms[roomId];
                var attempts = 0;
                var maxAttempts = 30;
                var placedCount = 0;
                var maxFurniture = roomId === 'A' ? 8 : 4; // 房间A更大，可以放更多家具

                while (attempts < maxAttempts && placedCount < maxFurniture) {
                    var furnitureIndex = Math.floor(Math.random() * furnitureData.length);
                    var furniture = furnitureData[furnitureIndex];
                    var maxX = Math.max(0, room.size.x - furniture.baseSize.x);
                    var maxY = Math.max(0, room.size.y - furniture.baseSize.y);
                    var x = Math.floor(Math.random() * (maxX + 1));
                    var y = Math.floor(Math.random() * (maxY + 1));

                    if (canPlaceFurniture(x, y, furniture.baseSize, furniture.properties.isWallDecoration, roomId)) {
                        var furnitureInstance = {
                            id: 'furniture_' + Date.now() + '_' + roomId + '_' + placedCount,
                            templateId: furniture.id,
                            position: { x: x, y: y },
                            rotation: Rotation.Deg0,
                            currentSize: furniture.baseSize,
                            placedTime: Date.now(),
                            room: roomId
                        };

                        placedFurnitures.push(furnitureInstance);
                        placedCount++;
                        totalPlaced++;
                    }

                    attempts++;
                }
            });

            renderRooms();
            updateScore();
            updateRoomCounts();
            showStatus(`随机摆放完成，共放置了 ${totalPlaced} 个家具`, 'success');
        }

        // 页面加载完成后初始化
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
