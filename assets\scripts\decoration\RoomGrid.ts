/**
 * 房间格子管理器
 * 负责管理房间的格子状态和占用情况
 */

import { Vec2 } from "cc";
import { GridState, PlacedFurniture, getFurnitureOccupiedPositions, isPositionInRoom } from "./DecorationDefine";

export class RoomGrid {
    private gridLayout!: GridState[][];
    private originalLayout!: GridState[][]; // 保存原始布局状态
    private roomSize!: Vec2;
    private furnitureOccupancy: Map<string, Vec2[]> = new Map(); // 家具ID -> 占用位置列表

    constructor(initialLayout: number[][]) {
        this.initializeGrid(initialLayout);
    }

    /**
     * 初始化房间格子
     */
    private initializeGrid(layout: number[][]) {
        this.roomSize = new Vec2(layout[0].length, layout.length);
        this.gridLayout = [];
        this.originalLayout = [];

        for (let y = 0; y < layout.length; y++) {
            this.gridLayout[y] = [];
            this.originalLayout[y] = [];
            for (let x = 0; x < layout[y].length; x++) {
                const gridState = layout[y][x] as GridState;
                this.gridLayout[y][x] = gridState;
                this.originalLayout[y][x] = gridState; // 保存原始状态
            }
        }
    }

    /**
     * 获取房间尺寸
     */
    getRoomSize(): Vec2 {
        return this.roomSize.clone();
    }

    /**
     * 获取指定位置的格子状态
     */
    getGridState(position: Vec2): GridState {
        if (!isPositionInRoom(position, this.roomSize)) {
            return GridState.Obstacle; // 超出范围视为障碍
        }
        return this.gridLayout[position.y][position.x];
    }

    /**
     * 设置指定位置的格子状态
     */
    private setGridState(position: Vec2, state: GridState): void {
        if (isPositionInRoom(position, this.roomSize)) {
            this.gridLayout[position.y][position.x] = state;
        }
    }

    /**
     * 检查指定区域是否可以放置家具
     */
    canPlaceFurniture(position: Vec2, size: Vec2, excludeFurnitureId?: string): boolean {
        // 检查是否超出房间边界
        if (position.x + size.x > this.roomSize.x || position.y + size.y > this.roomSize.y) {
            return false;
        }

        // 检查每个格子是否可用
        for (let x = 0; x < size.x; x++) {
            for (let y = 0; y < size.y; y++) {
                const checkPos = new Vec2(position.x + x, position.y + y);
                const gridState = this.getGridState(checkPos);

                // 如果是障碍物，不能放置
                if (gridState === GridState.Obstacle) {
                    return false;
                }

                // 如果被其他家具占用，检查是否是要排除的家具
                if (gridState === GridState.Occupied) {
                    if (!excludeFurnitureId || !this.isPositionOccupiedByFurniture(checkPos, excludeFurnitureId)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 检查指定区域是否可以放置挂饰类家具
     */
    canPlaceWallDecoration(position: Vec2, size: Vec2, excludeFurnitureId?: string): boolean {
        // 检查是否超出房间边界
        if (position.x + size.x > this.roomSize.x || position.y + size.y > this.roomSize.y) {
            return false;
        }

        // 检查每个格子是否可用，挂饰类家具只能放在邻墙格子上
        for (let x = 0; x < size.x; x++) {
            for (let y = 0; y < size.y; y++) {
                const checkPos = new Vec2(position.x + x, position.y + y);
                const gridState = this.getGridState(checkPos);

                // 挂饰类家具只能放在邻墙格子上
                if (gridState !== GridState.WallAdjacent) {
                    // 如果被其他家具占用，检查是否是要排除的家具
                    if (gridState === GridState.Occupied) {
                        if (!excludeFurnitureId || !this.isPositionOccupiedByFurniture(checkPos, excludeFurnitureId)) {
                            return false;
                        }
                    } else {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 检查位置是否被指定家具占用
     */
    private isPositionOccupiedByFurniture(position: Vec2, furnitureId: string): boolean {
        const occupiedPositions = this.furnitureOccupancy.get(furnitureId);
        if (!occupiedPositions) return false;
        
        return occupiedPositions.some(pos => pos.x === position.x && pos.y === position.y);
    }

    /**
     * 放置家具，占用相应格子
     */
    placeFurniture(furniture: PlacedFurniture, isWallDecoration: boolean = false): boolean {
        const occupiedPositions = getFurnitureOccupiedPositions(furniture);

        // 根据家具类型选择合适的验证方法
        let canPlace: boolean;
        if (isWallDecoration) {
            canPlace = this.canPlaceWallDecoration(furniture.position, furniture.currentSize);
        } else {
            canPlace = this.canPlaceFurniture(furniture.position, furniture.currentSize);
        }

        if (!canPlace) {
            return false;
        }

        // 占用格子
        occupiedPositions.forEach(pos => {
            this.setGridState(pos, GridState.Occupied);
        });

        // 记录家具占用信息
        this.furnitureOccupancy.set(furniture.id, occupiedPositions);

        return true;
    }

    /**
     * 移除家具，释放相应格子
     */
    removeFurniture(furnitureId: string): boolean {
        const occupiedPositions = this.furnitureOccupancy.get(furnitureId);
        if (!occupiedPositions) {
            return false;
        }

        // 释放格子（恢复为原始状态）
        occupiedPositions.forEach(pos => {
            // 恢复到原始布局状态
            const originalState = this.originalLayout[pos.y][pos.x];
            this.setGridState(pos, originalState);
        });

        // 移除占用记录
        this.furnitureOccupancy.delete(furnitureId);

        return true;
    }

    /**
     * 移动家具
     */
    moveFurniture(furnitureId: string, newFurniture: PlacedFurniture, isWallDecoration: boolean = false): boolean {
        // 先移除旧位置
        if (!this.removeFurniture(furnitureId)) {
            return false;
        }

        // 尝试放置到新位置
        if (this.placeFurniture(newFurniture, isWallDecoration)) {
            return true;
        } else {
            // 如果新位置放置失败，需要恢复旧位置
            // 这里需要保存旧的家具信息来恢复
            console.error("移动家具失败，无法恢复旧位置");
            return false;
        }
    }

    /**
     * 获取所有被占用的位置
     */
    getOccupiedPositions(): Vec2[] {
        const allOccupied: Vec2[] = [];
        this.furnitureOccupancy.forEach(positions => {
            allOccupied.push(...positions);
        });
        return allOccupied;
    }

    /**
     * 获取指定家具占用的位置
     */
    getFurnitureOccupiedPositions(furnitureId: string): Vec2[] {
        return this.furnitureOccupancy.get(furnitureId) || [];
    }

    /**
     * 清空所有家具占用
     */
    clearAllFurniture(): void {
        this.furnitureOccupancy.forEach((_, furnitureId) => {
            this.removeFurniture(furnitureId);
        });
    }

    /**
     * 获取房间格子状态的副本（用于调试）
     */
    getGridLayoutCopy(): GridState[][] {
        return this.gridLayout.map(row => [...row]);
    }

    /**
     * 打印房间状态（调试用）
     */
    printRoomState(): void {
        console.log("房间状态:");
        for (let y = 0; y < this.roomSize.y; y++) {
            let row = "";
            for (let x = 0; x < this.roomSize.x; x++) {
                row += this.gridLayout[y][x] + " ";
            }
            console.log(row);
        }
    }
}
